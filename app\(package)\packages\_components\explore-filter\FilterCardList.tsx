"use client";
import {
  Building,
  CarFront,
  IndianRupee,
  MapPin,
  Clock,
  Users,
  Plane,
  Utensils,
  Heart,
} from "lucide-react";
import Image from "next/image";
import { PackageType } from "@/app/types/package";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import { cn, formatIndianNumber } from "@/lib/utils";

import { useMemo, useState } from "react";
import { HotelMeal, Vehicle } from "@/app/types/pack";

// Helper function to get meal plan description
const getMealPlanDescription = (code: string) => {
  if (typeof code !== "string") {
    return "N/A";
  }
  switch (code.toUpperCase()) {
    case "EP":
      return "Room Only";
    case "CP":
      return "Breakfast Included";
    case "MAP":
      return "Breakfast and Dinner Included";
    case "AP":
      return "All Meals Included";
    default:
      return code;
  }
};

const FilterCardList = (props: { package: PackageType }) => {
  console.log("Vehicle data:", props.package.vehicle);
  const {
    packageImg,
    packageName,
    planName,
    destination,
    noOfDays,
    noOfNight,
    vehicle,
    hotel,
    hotelCount,
    perPerson,
    hotelMeal,
  } = props.package;

  const [isWishlisted, setIsWishlisted] = useState(false);

  const uniqueMealPlans = useMemo(() => {
    if (!hotelMeal || hotelMeal.length === 0) {
      return [];
    }

    const mealPlanCounts = hotelMeal.reduce((acc, meal) => {
      const description = getMealPlanDescription(meal.mealPlan);
      if (description !== "N/A") {
        acc[description] = (acc[description] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const plans = Object.keys(mealPlanCounts);

    if (
      plans.includes("Breakfast Included") &&
      plans.includes("Breakfast and Dinner Included")
    ) {
      if (
        mealPlanCounts["Breakfast and Dinner Included"] >=
        mealPlanCounts["Breakfast Included"]
      ) {
        return plans.filter((plan) => plan !== "Breakfast Included");
      } else {
        return plans.filter((plan) => plan !== "Breakfast and Dinner Included");
      }
    }

    return plans;
  }, [hotelMeal]);

  const mealPlanDisplay =
    uniqueMealPlans.length > 1
      ? uniqueMealPlans.filter((plan) => plan !== "N/A").join(", ")
      : uniqueMealPlans.join(", ");

  const amenities = useMemo(() => {
    const allAmenities = new Set<string>();
    hotel?.forEach((h) => h.amenities.forEach((a) => allAmenities.add(a)));
    return Array.from(allAmenities)
      .sort(() => 0.5 - Math.random())
      .slice(0, 3);
  }, [hotel]);

  const handleWishlistClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
    // Add wishlist logic here
  };

  return (
    <div
      className={cn(
        "bg-white rounded-2xl shadow-md overflow-hidden cursor-pointer transition-all duration-500 w-full max-w-sm mx-auto flex flex-col mb-6 border border-gray-200/60 backdrop-blur-sm",
        "hover:shadow-xl hover:-translate-y-3 hover:scale-[1.02] hover:border-gray-300/80",
        "transform-gpu will-change-transform",
        props.package.planName === "Gold"
          ? "border-[#EF831E]/60 hover:border-[#EF831E] hover:shadow-[0_8px_30px_rgba(239,131,30,0.15)] hover:ring-2 hover:ring-[#EF831E]/20"
          : props.package.planName === "Silver"
          ? "border-[#95A1AF]/60 hover:border-[#95A1AF] hover:shadow-[0_8px_30px_rgba(149,161,175,0.15)] hover:ring-2 hover:ring-[#95A1AF]/20"
          : props.package.planName === "Platinum"
          ? "border-[#CA0B0B]/60 hover:border-[#CA0B0B] hover:shadow-[0_8px_30px_rgba(202,11,11,0.15)] hover:ring-2 hover:ring-[#CA0B0B]/20"
          : "hover:border-green-500/80 hover:shadow-[0_8px_30px_rgba(34,197,94,0.15)] hover:ring-2 hover:ring-green-500/20"
      )}
    >
      <div className="relative w-full h-52 overflow-hidden">
        <Image
          src={NEXT_PUBLIC_IMAGE_URL + packageImg[0]}
          alt={packageName}
          fill
          className="object-cover group-hover:scale-110 transition-transform duration-500"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
        <div className={cn(
            "absolute top-3 left-3 flex justify-center shadow-lg px-3 py-1.5 text-xs font-semibold text-white rounded-full backdrop-blur-sm",
            "border border-white/20 transition-all duration-300 hover:scale-105",
            props.package.planName === "Gold"
              ? "bg-gradient-to-r from-[#EF831E] to-[#F59E0B] shadow-[#EF831E]/30"
              : props.package.planName === "Silver"
              ? "bg-gradient-to-r from-[#95A1AF] to-[#6B7280] shadow-[#95A1AF]/30"
              : props.package.planName === "Platinum"
              ? "bg-gradient-to-r from-[#CA0B0B] to-[#DC2626] shadow-[#CA0B0B]/30"
              : "bg-gradient-to-r from-[#1EC089] to-[#10B981] shadow-[#1EC089]/30"
          )}
        >
          {planName}
        </div>
      {/* <button
          onClick={handleWishlistClick}
          className="absolute top-3 right-3 bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors"
        >
          <Heart
            size={20}
            className={`transition-colors ${
              isWishlisted ? "text-red-500 fill-current" : "text-gray-500"
            }`}
          />
        </button>*/}
      </div>
      <div className="p-5 flex flex-col flex-grow">
        <h3 className="text-[#1EC089] text-xl font-semibold leading-tight mb-3 line-clamp-2 hover:text-[#16A085] transition-colors duration-300">
          {packageName}
        </h3>
        <div className="space-y-2.5 mb-4">
          <div className="flex items-center text-gray-700">
            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-3">
              <MapPin size={16} className="text-green-600" />
            </div>
            <span className="text-sm font-medium">
                {destination?.filter(d => d.noOfNight > 0).map((dest, i, arr) => (
                    <span key={i} className="inline-flex items-center">
                      <span className="text-[#FF7865] font-semibold text-sm">
                        {dest.noOfNight}N
                      </span>
                      <span className="text-gray-700 font-medium text-sm ml-1">
                        - {dest.destinationName}
                      </span>
                      {i < arr.length - 1 && (
                        <span className="text-gray-400 mx-2 font-normal">•</span>
                      )}
                    </span>
                ))}
            </span>
          
        </div>

        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center text-gray-700">
            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-3">
              <Clock size={16} className="text-blue-600" />
            </div>
            <span className="text-sm font-medium">
              {noOfNight} Nights, {noOfDays} Days
            </span>
          </div>
          <div className="text-xs bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-full px-3 py-1.5 flex items-center space-x-1">
            <span className="text-gray-600 font-medium">Starts</span>
            <span className="text-gray-500">@</span>
            <span className="text-[#1EC089] font-semibold">
              {props.package.startFrom}
            </span>
          </div>
        </div>
        <div className="border-t border-gray-100 pt-4 mb-4">
          <p className="font-semibold text-gray-800 mb-3 text-base">What's Included:</p>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
              <div className="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center">
                <Plane size={16} className="text-blue-600" />
              </div>
              <span className="text-gray-700 font-medium">Airport Transfer</span>
            </div>
            <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
              <div className="w-4 h-4 rounded-full bg-purple-100 flex items-center justify-center">
                <Building size={16} className="text-purple-600" />
              </div>
              <span className="text-gray-700 font-medium">{hotelCount} Hotels</span>
            </div>
            <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
              <div className="w-4 h-4 rounded-full bg-orange-100 flex items-center justify-center">
                <Utensils size={16} className="text-orange-600" />
              </div>
              <span className="text-gray-700 font-medium">{mealPlanDisplay}</span>
            </div>
            <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
              <div className="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center">
                <CarFront size={16} className="text-green-600" />
              </div>
              <span className="text-gray-700 font-medium">
                {vehicle && vehicle.length > 0 ? `${vehicle[0].seater} Seater` : "Cab"}
              </span>
            </div>
          </div>
        </div>

        <div className="flex-grow"></div>

        <div className="flex justify-between items-center pt-4 border-t border-gray-100">
          <div className="flex flex-col">
            <div className="flex items-center">
              <IndianRupee size={20} className="text-app-primary mr-1" />
              <span className="text-2xl font-bold text-app-primary">
                {formatIndianNumber(perPerson)}
              </span>
            </div>
            <p className="text-xs text-gray-500 ml-6">per person</p>
          </div>
          <button
            className="bg-gradient-to-r from-app-secondary to-emerald-600 text-white font-semibold px-4 py-2.5 text-sm rounded-xl hover:from-emerald-600 hover:to-app-secondary transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 hover:-translate-y-0.5"
          >
            View Details
          </button>
        </div>
      </div>
    </div>
    </div>
  );
};

export default FilterCardList;
