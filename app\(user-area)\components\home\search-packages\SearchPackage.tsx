"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search, MapPin, Calendar as CalendarIcon, Users, Sparkles, CheckCircle2, XCircle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useQuery } from 'react-query';
import { getInterest } from '@/app/actions/get-interest';
import { useMediaQuery } from 'react-responsive';

// Define interfaces
interface Destination {
  destinationId: string;
  destinationName: string;
  image?: string;
  popular?: boolean;
  imageError?: boolean;
}

interface Interest {
  image: string;
  interestId: string;
  interestName: string;
  isFirst: boolean;
  sort: number;
  _id: string;
}

interface Guests {
  adults: number;
  children: number;
  infants: number;
  pets: number;
}

const SearchPackage = () => {
  const router = useRouter();
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });

  // State management
  const [activeField, setActiveField] = useState<string | null>(null);
  const [location, setLocation] = useState('');
  const [date, setDate] = useState<Date | undefined>();
  const [guests, setGuests] = useState<Guests>({
    adults: 1,
    children: 0,
    infants: 0,
    pets: 0
  });
  const [selectedTheme, setSelectedTheme] = useState('');
  const [selectedThemeId, setSelectedThemeId] = useState('');

  // Destination search states
  const [allDestinations, setAllDestinations] = useState<Destination[]>([]);
  const [showDestinationPicker, setShowDestinationPicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch themes
  const { data: themes, isLoading: themesLoading } = useQuery<Interest[]>("fetch Interest", getInterest);

  const navigate = (path: string) => {
    router.push(path);
  };

  // Fetch destinations from API
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('https://api.tripxplo.com/v1/api/user/package/destination/search');
        const data = await response.json();
        if (data.result) {
          const destinationsWithImages = data.result.map((dest: any) => ({
            ...dest,
            image: dest.image ? `https://tripemilestone.in-maa-1.linodeobjects.com/${dest.image}` : null,
            popular: ['Bali', 'Goa', 'Manali', 'Varkala', 'Kashmir', 'Kerala'].some(popular =>
              dest.destinationName.toLowerCase().includes(popular.toLowerCase())
            ),
            imageError: false
          }));
          setAllDestinations(destinationsWithImages);
        }
      } catch (error) {
        console.error('Error fetching destinations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  // Featured destinations for mobile dropdown
  const FEATURED_DESTINATIONS = [
    { name: 'Goa', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800', isDomestic: true },
    { name: 'Kashmir', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800', isDomestic: true },
    { name: 'Manali', tag:'HONEYMOON', color: 'bg-green-100 text-green-800', isDomestic: true },
    { name: 'Ooty', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: true },
    { name: 'Bali', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800', isDomestic: false },
    { name: 'Maldives', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800', isDomestic: false },
    { name: 'Thailand', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: false },
  ];

  const handleGuestChange = (type: keyof Guests, increment: boolean) => {
    setGuests(prev => ({
      ...prev,
      [type]: increment ? prev[type] + 1 : Math.max(0, prev[type] - 1)
    }));
  };

  const getGuestText = () => {
    const total = guests.adults + guests.children + guests.infants;
    if (total === 0) return "Add guests";
    if (total === 1) return "1 guest";
    return `${total} guests`;
  };

  const handleDestinationSelect = (destination: string) => {
    setLocation(destination);
    setShowDestinationPicker(false);
    setActiveField(null);
  };

  const handleSearch = () => {
    console.log("Search:", { location, date, guests, selectedTheme });
    // Navigate to search results or handle search logic
    navigate("/search-progress");
  };

  return (
    <section id="hero-section" className="relative w-full h-[500px] sm:h-[600px] min-h-[500px] sm:min-h-[600px] flex flex-col items-center justify-center overflow-hidden">
      {/* Background with a more vibrant and inviting travel image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat h-full"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1512100356356-de1b84283e18?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/70 h-full"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 max-w-5xl mx-auto">
        <h1 className="text-4xl md:text-7xl font-semibold text-white mb-4 leading-tight animate-fade-in-up">
          Seamless Travel, <span className="text-emerald-400 font-extrabold italic">Unforgettable</span> Experiences
        </h1>

        {/* New Airbnb-style Search Bar */}
        <div className="w-full max-w-4xl mx-auto mb-12 mt-8">
          <div className="bg-white rounded-full shadow-lg border border-gray-200 p-2 flex items-center">
            {/* Where */}
            <div
              className={`flex-1 px-6 py-4 rounded-full cursor-pointer transition-colors ${
                activeField === "where" ? "bg-white shadow-lg" : "hover:bg-gray-50"
              }`}
              onClick={() => setActiveField(activeField === "where" ? null : "where")}
            >
              <div className="flex flex-col">
                <label className="text-xs font-semibold text-gray-900 mb-1">Where</label>
                <Input
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder="Search destinations"
                  className="border-0 p-0 text-sm text-gray-600 placeholder:text-gray-400 focus-visible:ring-0 bg-transparent"
                  onFocus={() => isMobile && setShowDestinationPicker(true)}
                />
              </div>

              {/* Mobile Destination Dropdown */}
              {isMobile && showDestinationPicker && (
                <div className="absolute top-full left-0 right-0 bg-white rounded-2xl shadow-2xl mt-2 z-50 p-4 border max-h-96 overflow-y-auto">
                  {/* Domestic Section */}
                  <div className="mb-4">
                    <h4 className="text-sm font-bold text-blue-600 mb-2 px-3 py-2 bg-blue-50">🇮🇳 DOMESTIC DESTINATIONS</h4>
                    <div className="space-y-1">
                      {FEATURED_DESTINATIONS.filter(dest => dest.isDomestic).map((dest, index) => (
                        <div
                          key={`domestic-${index}`}
                          onClick={() => handleDestinationSelect(dest.name)}
                          className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer transition-colors group"
                        >
                          <span className="text-gray-800 font-medium group-hover:text-gray-900">
                            {dest.name}
                          </span>
                          {dest.tag && (
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${dest.color}`}>
                              {dest.tag}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* International Section */}
                  <div className="border-t pt-4">
                    <h4 className="text-sm font-bold text-green-600 mb-2 px-3 py-2 bg-green-50">🌍 INTERNATIONAL DESTINATIONS</h4>
                    <div className="space-y-1">
                      {FEATURED_DESTINATIONS.filter(dest => !dest.isDomestic).map((dest, index) => (
                        <div
                          key={`international-${index}`}
                          onClick={() => handleDestinationSelect(dest.name)}
                          className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer transition-colors group"
                        >
                          <span className="text-gray-800 font-medium group-hover:text-gray-900">
                            {dest.name}
                          </span>
                          {dest.tag && (
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${dest.color}`}>
                              {dest.tag}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Divider */}
            <div className="w-px h-8 bg-gray-300"></div>

            {/* Date */}
            <Popover>
              <PopoverTrigger asChild>
                <div
                  className={`flex-1 px-6 py-4 rounded-full cursor-pointer transition-colors ${
                    activeField === "date" ? "bg-white shadow-lg" : "hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveField(activeField === "date" ? null : "date")}
                >
                  <div className="flex flex-col">
                    <label className="text-xs font-semibold text-gray-900 mb-1">Date</label>
                    <span className="text-sm text-gray-600">{date ? format(date, "MMM d") : "Add date"}</span>
                  </div>
                </div>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  disabled={(date) => date < new Date()}
                  initialFocus
                  className="w-full"
                  classNames={{
                    table: "w-full border-spacing-1",
                    head_cell: "w-full md:w-[50px] font-semibold text-sm md:text-base text-gray-700 pb-2 text-center",
                    cell: "w-full h-10 sm:w-[51px] md:h-[50px] lg:w-[50px] md:h-[50px] p-0.5",
                    row: "flex w-full justify-stretch gap-6",
                    day: "rounded-xl w-full h-full text-sm md:text-base font-medium hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 hover:scale-105 hover:shadow-sm flex items-center justify-center",
                    day_selected: "bg-gradient-to-br from-[#ff7865] to-[#f95d47] text-white rounded-xl shadow-lg hover:shadow-xl transform scale-105",
                    day_today: "bg-gradient-to-br from-[#23cd92] to-[#1fb584] text-white rounded-xl shadow-md",
                    day_disabled: "opacity-40 cursor-not-allowed hover:bg-transparent hover:text-gray-400 hover:scale-100",
                    caption_label: "text-lg md:text-xl font-bold text-gray-800 mb-2",
                    nav_button: "rounded-full w-8 h-8 md:w-10 md:h-10 hover:bg-gray-100 transition-all duration-200 hover:scale-110 border border-gray-200 shadow-sm",
                    nav_button_previous: " hover:bg-blue-50 hover:border-blue-200",
                    nav_button_next: "hover:bg-blue-50 hover:border-blue-200",
                    caption: "flex justify-center items-center mb-4 relative",
                  }}
                />
              </PopoverContent>
            </Popover>

            {/* Divider */}
            <div className="w-px h-8 bg-gray-300"></div>

            {/* Who */}
            <Popover>
              <PopoverTrigger asChild>
                <div
                  className={`flex-1 px-6 py-4 rounded-full cursor-pointer transition-colors ${
                    activeField === "who" ? "bg-white shadow-lg" : "hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveField(activeField === "who" ? null : "who")}
                >
                  <div className="flex flex-col">
                    <label className="text-xs font-semibold text-gray-900 mb-1">Who</label>
                    <span className="text-sm text-gray-600">{getGuestText()}</span>
                  </div>
                </div>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-6" align="end">
                <div className="space-y-6">
                  {/* Themes Selection */}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Sparkles className="w-4 h-4 text-orange-500" />
                      Trip Theme
                    </h4>
                    {themesLoading ? (
                      <div className="grid grid-cols-3 gap-2">
                        {[...Array(6)].map((_, index) => (
                          <div key={index} className="w-full animate-pulse h-16 bg-slate-200 rounded-lg"></div>
                        ))}
                      </div>
                    ) : (
                      <div className="grid grid-cols-3 gap-2 mb-4">
                        {themes?.slice(0, 6).map((theme) => {
                          const isSelected = selectedTheme === theme.interestName;
                          return (
                            <Card
                              key={theme._id}
                              className={cn(
                                'cursor-pointer transition-all duration-300 rounded-lg overflow-hidden group border',
                                isSelected
                                  ? 'ring-2 ring-[#ff7865] shadow-md bg-gradient-to-br from-orange-100 via-red-100 to-pink-100 border-[#ff7865]'
                                  : 'border-gray-200 hover:border-[#ff7865]/50 hover:shadow-sm'
                              )}
                              onClick={() => {
                                setSelectedTheme(theme.interestName);
                                setSelectedThemeId(theme.interestId);
                              }}
                            >
                              <CardContent className="p-2 flex flex-col items-center justify-center h-16 relative">
                                {isSelected && (
                                  <div className="absolute top-1 right-1 w-3 h-3 bg-[#ff7865] rounded-full flex items-center justify-center">
                                    <CheckCircle2 className="w-2 h-2 text-white" />
                                  </div>
                                )}
                                <div className="w-6 h-6 mb-1 flex items-center justify-center">
                                  {theme.image ? (
                                    <img
                                      src={`https://tripemilestone.in-maa-1.linodeobjects.com/${theme.image}`}
                                      alt={theme.interestName}
                                      className="w-full h-full object-contain rounded-full"
                                    />
                                  ) : (
                                    <Sparkles className="w-4 h-4 text-gray-400" />
                                  )}
                                </div>
                                <span className="text-xs font-medium text-center text-gray-700">
                                  {theme.interestName}
                                </span>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    )}
                  </div>

                  {/* Adults */}
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Adults</div>
                      <div className="text-sm text-gray-500">Ages 13 or above</div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 bg-transparent"
                        onClick={() => handleGuestChange("adults", false)}
                        disabled={guests.adults === 0}
                      >
                        -
                      </Button>
                      <span className="w-8 text-center">{guests.adults}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 bg-transparent"
                        onClick={() => handleGuestChange("adults", true)}
                      >
                        +
                      </Button>
                    </div>
                  </div>

                  {/* Children */}
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Children</div>
                      <div className="text-sm text-gray-500">Ages 2-12</div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 bg-transparent"
                        onClick={() => handleGuestChange("children", false)}
                        disabled={guests.children === 0}
                      >
                        -
                      </Button>
                      <span className="w-8 text-center">{guests.children}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 bg-transparent"
                        onClick={() => handleGuestChange("children", true)}
                      >
                        +
                      </Button>
                    </div>
                  </div>

                  {/* Infants */}
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Infants</div>
                      <div className="text-sm text-gray-500">Under 2</div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 bg-transparent"
                        onClick={() => handleGuestChange("infants", false)}
                        disabled={guests.infants === 0}
                      >
                        -
                      </Button>
                      <span className="w-8 text-center">{guests.infants}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 bg-transparent"
                        onClick={() => handleGuestChange("infants", true)}
                      >
                        +
                      </Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            {/* Search Button */}
            <Button
              className="ml-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-12 h-12 p-0 flex items-center justify-center"
              onClick={handleSearch}
            >
              <Search className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced floating elements for a more dynamic feel */}
      <div className="absolute top-20 left-10 w-28 h-28 bg-emerald-400/20 backdrop-blur-sm rounded-full animate-pulse-slow hidden lg:block"></div>
      <div className="absolute bottom-32 right-16 w-24 h-24 bg-blue-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-700 hidden lg:block"></div>
      <div className="absolute top-1/3 right-24 w-20 h-20 bg-purple-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-300 hidden lg:block"></div>
      <div className="absolute bottom-1/4 left-24 w-16 h-16 bg-yellow-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-1000 hidden lg:block"></div>
    </section>
  );
};

export default SearchPackage;
